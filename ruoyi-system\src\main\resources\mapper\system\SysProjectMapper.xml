<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysProjectMapper">

    <resultMap type="SysProject" id="SysProjectResult">
        <id     property="projectId"      column="project_id"      />
        <result property="projectName"    column="project_name"    />
        <result property="status"         column="status"          />
        <result property="createBy"       column="create_by"       />
        <result property="createTime"     column="create_time"     />
        <result property="updateBy"       column="update_by"       />
        <result property="updateTime"     column="update_time"     />
        <result property="remark"         column="remark"          />
    </resultMap>
    
    <sql id="selectProjectVo">
        select project_id, project_name, status, create_by, create_time, update_by, update_time, remark
        from sys_project
    </sql>
    
    <select id="selectProjectList" parameterType="SysProject" resultMap="SysProjectResult">
        <include refid="selectProjectVo"/>
        <where>
            <if test="projectName != null and projectName != ''">
                AND project_name like concat('%', #{projectName}, '%')
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
        </where>
    </select>
    
    <select id="selectProjectAll" resultMap="SysProjectResult">
        <include refid="selectProjectVo"/>
    </select>
    
    <select id="selectProjectById" parameterType="Long" resultMap="SysProjectResult">
        <include refid="selectProjectVo"/>
        where project_id = #{projectId}
    </select>
    
    <select id="selectProjectByName" parameterType="String" resultMap="SysProjectResult">
        <include refid="selectProjectVo"/>
        where project_name = #{projectName}
    </select>
    
    <insert id="insertProject" parameterType="SysProject" useGeneratedKeys="true" keyProperty="projectId">
        insert into sys_project(
            <if test="projectName != null and projectName != ''">project_name,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            create_time
        )values(
            <if test="projectName != null and projectName != ''">#{projectName},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            sysdate()
        )
    </insert>
    
    <update id="updateProject" parameterType="SysProject">
        update sys_project
        <set>
            <if test="projectName != null and projectName != ''">project_name = #{projectName},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        where project_id = #{projectId}
    </update>
    
    <delete id="deleteProjectById" parameterType="Long">
        delete from sys_project where project_id = #{projectId}
    </delete>
    
    <delete id="deleteProjectByIds" parameterType="Long">
        delete from sys_project where project_id in 
        <foreach collection="array" item="projectId" open="(" separator="," close=")">
            #{projectId}
        </foreach>
    </delete>
    
</mapper>
