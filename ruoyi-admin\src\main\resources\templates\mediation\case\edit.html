<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('修改案件')" />
    <th:block th:include="include :: select2-css" />
    <th:block th:include="include :: datetimepicker-css" />
    <style>
        .case-modal-input {
            height: 38px;
            border-radius: 4px;
            border: 1px solid #e5e6eb;
            font-size: 16px;
            color: #222;
            background: #fafbfc;
            box-shadow: none;
            padding-left: 10px;
            padding-right: 10px;
            transition: border-color 0.2s;
        }
        .case-modal-input:focus {
            border-color: #409eff;
            background: #fff;
            outline: none;
        }
        .case-modal-label {
            font-size: 16px;
            color: #222;
            font-weight: 600;
            margin-bottom: 6px;
            display: block;
        }
        .case-modal-form-row {
            margin-bottom: 18px;
        }
    </style>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-case-edit" th:object="${mediationCase}">            <input name="id" type="hidden" th:field="*{id}" />            <div class="form-group">
                <label class="col-sm-3 control-label case-modal-label">公司：</label>
                <div class="col-sm-8">
                    <select name="deptId" class="form-control deptSelectpicker case-modal-input" th:field="*{deptId}" th:attr="data-selected-id=*{deptId}"></select>
                </div>
            </div>
              <div class="form-group">
                <label class="col-sm-3 control-label case-modal-label">管理员：</label>
                <div class="col-sm-8">
                    <select name="adminId" class="form-control adminSelectpicker case-modal-input" th:field="*{adminId}" th:attr="data-selected-id=*{adminId}"></select>
                </div>
            </div>
              <div class="form-group">
                <label class="col-sm-3 control-label case-modal-label">渠道方：</label>
                <div class="col-sm-8">
                    <select name="channelId" class="form-control channelSelectpicker case-modal-input" th:field="*{channelId}" th:attr="data-selected-id=*{channelId}"></select>
                </div>
            </div>
              <div class="form-group">
                <label class="col-sm-3 control-label case-modal-label">项目：</label>
                <div class="col-sm-8">
                    <select name="projectId" class="form-control projectSelectpicker case-modal-input" th:field="*{projectId}" th:attr="data-selected-id=*{projectId}"></select>
                </div>
            </div>
              <div class="form-group">
                <label class="col-sm-3 control-label case-modal-label">调解员：</label>
                <div class="col-sm-8">
                    <select name="mediatorId" class="form-control mediatorSelectpicker case-modal-input" th:field="*{mediatorId}" th:attr="data-selected-id=*{mediatorId}"></select>
                </div>
            </div>
            
            <div class="form-group">
                <label class="col-sm-3 control-label case-modal-label">日期：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="caseDate" class="form-control case-modal-input" placeholder="yyyy-MM-dd" type="text" th:value="${#dates.format(mediationCase.caseDate, 'yyyy-MM-dd')}" />
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label class="col-sm-3 control-label case-modal-label">原告：</label>
                <div class="col-sm-8">
                    <input name="plaintiff" class="form-control case-modal-input" type="text" th:field="*{plaintiff}">
                </div>
            </div>
            
            <div class="form-group">
                <label class="col-sm-3 control-label case-modal-label">被告：</label>
                <div class="col-sm-8">
                    <input name="defendant" class="form-control case-modal-input" type="text" th:field="*{defendant}">
                </div>
            </div>
            
            <div class="form-group">
                <label class="col-sm-3 control-label case-modal-label">电话：</label>
                <div class="col-sm-8">
                    <input name="phone" class="form-control case-modal-input" type="text" th:field="*{phone}">
                </div>
            </div>
            
            <div class="form-group">
                <label class="col-sm-3 control-label case-modal-label">一刷备注：</label>
                <div class="col-sm-8">
                    <input name="remark1" class="form-control case-modal-input" type="text" th:field="*{remark1}">
                </div>
            </div>
            
            <div class="form-group">
                <label class="col-sm-3 control-label case-modal-label">二刷备注：</label>
                <div class="col-sm-8">
                    <input name="remark2" class="form-control case-modal-input" type="text" th:field="*{remark2}">
                </div>
            </div>
            
            <div class="form-group">
                <label class="col-sm-3 control-label case-modal-label">三刷备注：</label>
                <div class="col-sm-8">
                    <input name="remark3" class="form-control case-modal-input" type="text" th:field="*{remark3}">
                </div>
            </div>              <div class="form-group">
                <label class="col-sm-3 control-label case-modal-label">案件状态：</label>
                <div class="col-sm-8">
                    <select name="status" class="form-control case-modal-input statusSelectpicker" th:field="*{status}" th:attr="data-selected-id=*{status}">
                        <option value="1">处理中</option>
                        <option value="2">处理失败</option>
                        <option value="3">处理成功</option>
                        <option value="4">已确认</option>
                    </select>
                </div>
            </div>
            
            <div class="form-group">
                <label class="col-sm-3 control-label case-modal-label">处理成功日期：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="successDate" class="form-control case-modal-input" placeholder="yyyy-MM-dd" type="text" th:value="${#dates.format(mediationCase.successDate, 'yyyy-MM-dd')}" />
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label class="col-sm-3 control-label case-modal-label">项目名称：</label>
                <div class="col-sm-8">
                    <input name="projectName" class="form-control case-modal-input" type="text" th:field="*{projectName}">
                </div>
            </div>
            
            <div class="form-group">
                <label class="col-sm-3 control-label case-modal-label">备注：</label>
                <div class="col-sm-8">
                    <textarea name="note" class="form-control" rows="3" th:field="*{note}"></textarea>
                </div>
            </div>
        </form>
    </div>
    
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: select2-js" />
    <th:block th:include="include :: datetimepicker-js" />
      <script type="text/javascript">        var prefix = ctx + "mediation/case";          $(function() {
            try {
                // 先禁用select2中可能冲突的行为
                if($.fn.select2) {
                    $.fn.select2.defaults.set("disabled", true);
                }
            } catch(e) {
                console.log("Select2预处理错误", e);
            }
            
            // 初始化表单，延迟执行确保DOM完全加载
            setTimeout(function() {
                // 确保先删除可能存在的select2实例
                try {
                    $(".deptSelectpicker, .adminSelectpicker, .mediatorSelectpicker, .channelSelectpicker, .projectSelectpicker, .statusSelectpicker").select2('destroy');
                } catch(e) {
                    console.log("Select2实例销毁错误", e);
                }
                
                // 按顺序加载各个下拉菜单的数据
                loadDeptData();
                loadAdminData();
                loadMediatorData();
                loadChannelData();
                loadProjectData();
                initStatusSelect();
                
                // 检查所有下拉菜单的选中状态，确保显示正确
                setTimeout(function() {
                    ensureSelectValues();
                }, 500);
            }, 100);
            
            // 初始化日期选择器
            $('.date').datetimepicker({
                format: 'yyyy-mm-dd',
                minView: 'month',
                autoclose: true
            });
        });
        
        // 确保所有下拉菜单都显示正确的选中值
        function ensureSelectValues() {
            var selectors = [
                { selector: ".deptSelectpicker", name: "公司" },
                { selector: ".adminSelectpicker", name: "管理员" },
                { selector: ".mediatorSelectpicker", name: "调解员" },
                { selector: ".channelSelectpicker", name: "渠道方" },
                { selector: ".projectSelectpicker", name: "项目" },
                { selector: ".statusSelectpicker", name: "状态" }
            ];
            
            $.each(selectors, function(index, item) {
                var select = $(item.selector);
                var dataId = select.data("selected-id");
                var currentVal = select.val();
                
                console.log("检查 " + item.name + " 选中状态:", { 
                    "data-id": dataId, 
                    "current": currentVal 
                });
                
                if (dataId && (!currentVal || currentVal != dataId)) {
                    console.log("强制设置 " + item.name + " 选中值:", dataId);
                    select.val(dataId).trigger('change');
                }
            });
        }/* 表单提交 */
        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-case-edit').serialize());
            }
        }        /* 加载部门数据 */        function loadDeptData(forcedValue) {
            var deptTreeSelect = $(".deptSelectpicker");
            // 优先从data属性获取，其次从传入参数获取，最后从元素的value获取
            var selectedValue = deptTreeSelect.data("selected-id") || forcedValue || deptTreeSelect.val();
            
            console.log("部门选择器初始值:", {
                "data-selected-id": deptTreeSelect.data("selected-id"),
                "forcedValue": forcedValue,
                "value": deptTreeSelect.val(),
                "最终使用": selectedValue
            });
            
            $.ajax({
                type: "GET",
                url: prefix + "/deptTree",
                dataType: "json",
                success: function(data) {
                    deptTreeSelect.empty();
                    deptTreeSelect.append("<option value=''>请选择公司</option>");
                    
                    $.each(data, function(index, dept) {
                        var selected = dept.id == selectedValue ? 'selected' : '';
                        deptTreeSelect.append("<option value='" + dept.id + "' " + selected + ">" + dept.name + "</option>");
                    });
                    
                    // 使用select2初始化，并强制设置选中值
                    deptTreeSelect.select2({
                        placeholder: "请选择公司",
                        allowClear: true
                    });
                    
                    if (selectedValue) {
                        console.log("设置部门选中值:", selectedValue);
                        deptTreeSelect.val(selectedValue).trigger('change');
                    }
                }
            });
        }        /* 加载管理员数据 */        function loadAdminData(forcedValue) {
            var adminSelect = $(".adminSelectpicker");
            // 优先从data属性获取，其次从传入参数获取，最后从元素的value获取
            var selectedValue = adminSelect.data("selected-id") || forcedValue || adminSelect.val();
            
            console.log("管理员选择器初始值:", {
                "data-selected-id": adminSelect.data("selected-id"),
                "forcedValue": forcedValue,
                "value": adminSelect.val(),
                "最终使用": selectedValue
            });
            
            $.ajax({
                type: "GET",
                url: prefix + "/adminUsers",
                dataType: "json",
                success: function(res) {
                    if (res.code == 0) {
                        var data = res.data;
                        adminSelect.empty();
                        adminSelect.append("<option value=''>请选择管理员</option>");
                        
                        $.each(data, function(index, user) {
                            var selected = user.userId == selectedValue ? 'selected' : '';
                            adminSelect.append("<option value='" + user.userId + "' " + selected + ">" + user.userName + "</option>");
                        });
                        
                        // 使用select2初始化，并强制设置选中值
                        adminSelect.select2({
                            placeholder: "请选择管理员",
                            allowClear: true
                        });
                        
                        if (selectedValue) {
                            console.log("设置管理员选中值:", selectedValue);
                            adminSelect.val(selectedValue).trigger('change');
                        }
                    }
                }
            });
        }        /* 加载调解员数据 */        function loadMediatorData(forcedValue) {
            var mediatorSelect = $(".mediatorSelectpicker");
            // 优先从data属性获取，其次从传入参数获取，最后从元素的value获取
            var selectedValue = mediatorSelect.data("selected-id") || forcedValue || mediatorSelect.val();
            
            console.log("调解员选择器初始值:", {
                "data-selected-id": mediatorSelect.data("selected-id"),
                "forcedValue": forcedValue,
                "value": mediatorSelect.val(),
                "最终使用": selectedValue
            });
            
            $.ajax({
                type: "GET",
                url: prefix + "/mediatorUsers",
                dataType: "json",
                success: function(res) {
                    if (res.code == 0) {
                        var data = res.data;
                        mediatorSelect.empty();
                        mediatorSelect.append("<option value=''>请选择调解员</option>");
                        
                        $.each(data, function(index, user) {
                            var selected = user.userId == selectedValue ? 'selected' : '';
                            mediatorSelect.append("<option value='" + user.userId + "' " + selected + ">" + user.userName + "</option>");
                        });
                        
                        // 使用select2初始化，并强制设置选中值
                        mediatorSelect.select2({
                            placeholder: "请选择调解员",
                            allowClear: true
                        });
                        
                        if (selectedValue) {
                            console.log("设置调解员选中值:", selectedValue);
                            mediatorSelect.val(selectedValue).trigger('change');
                        }
                    }
                }
            });
        }        /* 加载渠道方数据 */        function loadChannelData(forcedValue) {
            var channelSelect = $(".channelSelectpicker");
            // 优先从data属性获取，其次从传入参数获取，最后从元素的value获取
            var selectedValue = channelSelect.data("selected-id") || forcedValue || channelSelect.val();
            
            console.log("渠道方选择器初始值:", {
                "data-selected-id": channelSelect.data("selected-id"),
                "forcedValue": forcedValue,
                "value": channelSelect.val(),
                "最终使用": selectedValue
            });
            
            $.ajax({
                type: "GET",
                url: prefix + "/channelList",
                dataType: "json",
                success: function(res) {
                    if (res.code == 0) {
                        var data = res.data;
                        channelSelect.empty();
                        channelSelect.append("<option value=''>请选择渠道方</option>");
                        
                        $.each(data, function(index, channel) {
                            var selected = channel.channelId == selectedValue ? 'selected' : '';
                            channelSelect.append("<option value='" + channel.channelId + "' " + selected + ">" + channel.channelName + "</option>");
                        });
                        
                        // 使用select2初始化，并强制设置选中值
                        channelSelect.select2({
                            placeholder: "请选择渠道方",
                            allowClear: true
                        });
                        
                        if (selectedValue) {
                            console.log("设置渠道方选中值:", selectedValue);
                            channelSelect.val(selectedValue).trigger('change');
                        }
                    }
                }
            });
        }        /* 加载项目数据 */        function loadProjectData(forcedValue) {
            var projectSelect = $(".projectSelectpicker");
            // 优先从data属性获取，其次从传入参数获取，最后从元素的value获取
            var selectedValue = projectSelect.data("selected-id") || forcedValue || projectSelect.val();
            
            console.log("项目选择器初始值:", {
                "data-selected-id": projectSelect.data("selected-id"),
                "forcedValue": forcedValue,
                "value": projectSelect.val(),
                "最终使用": selectedValue
            });
            
            $.ajax({
                type: "GET",
                url: prefix + "/projectList",
                dataType: "json",
                success: function(res) {
                    if (res.code == 0) {
                        var data = res.data;
                        projectSelect.empty();
                        projectSelect.append("<option value=''>请选择项目</option>");
                        
                        $.each(data, function(index, project) {
                            var selected = project.projectId == selectedValue ? 'selected' : '';
                            projectSelect.append("<option value='" + project.projectId + "' " + selected + ">" + project.projectName + "</option>");
                        });
                        
                        // 使用select2初始化，并强制设置选中值
                        projectSelect.select2({
                            placeholder: "请选择项目",
                            allowClear: true
                        });
                        
                        if (selectedValue) {
                            console.log("设置项目选中值:", selectedValue);
                            projectSelect.val(selectedValue).trigger('change');
                        }
                    }
                }
            });
        }        /* 初始化状态下拉框 */
        function initStatusSelect(forcedValue) {
            var statusSelect = $(".statusSelectpicker");
            // 优先从data属性获取，其次从传入参数获取，最后从元素的value获取
            var selectedValue = statusSelect.data("selected-id") || forcedValue || statusSelect.val();
            
            console.log("状态选择器初始值:", {
                "data-selected-id": statusSelect.data("selected-id"),
                "forcedValue": forcedValue,
                "value": statusSelect.val(),
                "最终使用": selectedValue
            });
            
            // 使用select2初始化下拉框
            statusSelect.select2({
                placeholder: "请选择状态",
                allowClear: false,
                minimumResultsForSearch: Infinity // 禁用搜索功能，因为状态选项很少
            });
            
            // 强制触发一次选择事件，确保显示选中值
            if (selectedValue) {
                console.log("设置状态选中值:", selectedValue);
                setTimeout(function() {
                    statusSelect.val(selectedValue).trigger('change');
                }, 100);
            }
        }
    </script>
</body>
</html>
