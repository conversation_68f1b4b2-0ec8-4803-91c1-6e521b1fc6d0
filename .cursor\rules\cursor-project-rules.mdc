---
description: 
globs: 
alwaysApply: false
---

## 1. 项目结构说明
- 后端主包路径：`com.ruoyi`
- 主要模块：`ruoyi-admin`（前端与控制器）、`ruoyi-system`（系统业务）、`ruoyi-common`（通用）、`ruoyi-generator`（代码生成）、`ruoyi-quartz`（定时任务）
- 前端模板目录：`ruoyi-admin/src/main/resources/templates`，业务模块一般放于`system`子目录
- 静态资源目录：`ruoyi-admin/src/main/resources/static`
- MyBatis映射文件：`ruoyi-system/src/main/resources/mybatis`（如有）

## 2. 命名规范
- 实体类：首字母大写驼峰（如`SysUser`）
- Mapper接口：实体类名+Mapper（如`SysUserMapper`）
- Service接口：I+实体类名+Service（如`ISysUserService`）
- Controller：业务名+Controller（如`SysUserController`）
- 前端模板：业务名小写（如`user.html`）

## 3. 分层规范
- Controller层：负责接收请求、参数校验、调用Service、返回结果
- Service层：业务逻辑处理，接口与实现分离
- Mapper层：数据库访问，使用MyBatis
- Domain层：实体类，命名与表结构一致

## 4. 前后端交互规范
- Controller返回统一响应结构（如AjaxResult）
- 分页查询使用PageDomain、TableDataInfo
- 前端表格采用Bootstrap Table，增删改查均通过Ajax与后端交互

## 5. 数据库规范
- 表名统一小写、下划线分隔（如`sys_user`）
- 主键自增，命名为`id`
- 字段名小写、下划线分隔，类型与业务含义一致
- 建表SQL统一放于`sql`目录

## 6. 菜单与权限规范
- 菜单数据存储于`sys_menu`表
- 一级菜单、二级菜单、按钮权限均需配置
- 菜单路由与Controller、前端页面路径保持一致

## 7. 扩展建议
- 新业务模块建议以独立目录分层（如`调解管理`可新建`mediation`目录）
- 业务表、实体、Mapper、Service、Controller、前端模板一一对应

- 保持与现有系统风格、结构、命名一致 