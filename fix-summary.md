# RuoYi 系统错误修复总结

## 修复的问题

### 1. NoSuchMethodError: findUsersByRoleKey 方法不存在

**错误信息：**
```
java.lang.NoSuchMethodError: 'java.util.List com.ruoyi.system.service.ISysUserService.findUsersByRoleKey(java.lang.String)'
```

**问题分析：**
- `MediationCaseController` 中的 `getMediatorUsers()` 和 `getAdminUsers()` 方法尝试调用不存在的 `findUsersByRoleKey()` 方法
- 虽然接口和实现类中都有该方法，但可能存在编译版本不一致的问题

**解决方案：**
- 代码中已经实现了替代方案 `getUsersByRole()` 方法
- 该方法通过以下步骤获取用户：
  1. 根据角色标识查询角色信息
  2. 使用角色ID查询分配给该角色的用户列表

### 2. T<PERSON><PERSON>eaf 模板片段解析错误

**错误信息：**
```
Error resolving fragment: "~{include :: bootstrap-datetimepicker-js}": template or fragment could not be resolved
```

**问题分析：**
- `case.html` 模板中引用了不存在的片段 `bootstrap-datetimepicker-js`
- `include.html` 中实际的片段名称是 `datetimepicker-js`

**解决方案：**
- 修改 `case.html` 第185行：
  ```html
  <!-- 修改前 -->
  <th:block th:include="include :: bootstrap-datetimepicker-js" />
  
  <!-- 修改后 -->
  <th:block th:include="include :: datetimepicker-js" />
  ```
- 添加对应的CSS引用：
  ```html
  <th:block th:include="include :: datetimepicker-css" />
  ```

## 修改的文件

1. **ruoyi-admin/src/main/resources/templates/mediation/case/case.html**
   - 修复日期时间选择器片段引用
   - 添加必要的CSS引用

## 验证步骤

1. **重新编译项目**
   ```bash
   mvn clean compile
   ```

2. **重启应用服务器**

3. **测试相关功能**
   - 访问 `/mediation/case` 页面
   - 测试 `/mediation/case/mediatorUsers` 接口
   - 测试 `/mediation/case/adminUsers` 接口

## 预期结果

- 页面能够正常加载，不再出现模板解析错误
- 调解员和管理员用户列表接口能够正常返回数据
- 日期选择器功能正常工作

## 注意事项

- 如果仍然出现 NoSuchMethodError，可能需要清理编译缓存并重新编译整个项目
- 确保所有相关的模块都已重新编译
- 检查是否有其他地方也引用了错误的片段名称
