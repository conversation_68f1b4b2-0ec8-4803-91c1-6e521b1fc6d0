<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改渠道方')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-channel-edit" th:object="${channel}">
            <input name="channelId" th:field="*{channelId}" type="hidden">
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">渠道名称：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" name="channelName" id="channelName" th:field="*{channelName}" required>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">渠道状态：</label>
                <div class="col-sm-8">
                    <div class="radio-box" th:each="dict : ${@dict.getType('sys_normal_disable')}">
                        <input type="radio" th:id="${'status_' + dict.dictCode}" name="status" th:value="${dict.dictValue}" th:field="*{status}">
                        <label th:for="${'status_' + dict.dictCode}" th:text="${dict.dictLabel}"></label>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">备注：</label>
                <div class="col-sm-8">
                    <textarea id="remark" name="remark" th:field="*{remark}" class="form-control"></textarea>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
        var prefix = ctx + "system/channel";
        
        $("#form-channel-edit").validate({
            onkeyup: false,
            rules:{
                channelName:{
                    remote: {
                        url: prefix + "/checkChannelNameUnique",
                        type: "post",
                        dataType: "json",
                        data: {
                            "channelId": function() {
                                return $("input[name='channelId']").val();
                            },
                            "channelName": function() {
                                return $.common.trim($("#channelName").val());
                            }
                        },
                        dataFilter: function(data, type) {
                            return $.validate.unique(data);
                        }
                    }
                },
            },
            messages: {
                "channelName": {
                    remote: "渠道名称已经存在"
                }
            },
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-channel-edit').serialize());
            }
        }
    </script>
</body>
</html>
