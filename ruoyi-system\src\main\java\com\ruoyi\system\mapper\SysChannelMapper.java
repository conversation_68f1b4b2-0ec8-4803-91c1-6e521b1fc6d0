package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.SysChannel;

/**
 * 渠道方管理 数据层
 * 
 */
public interface SysChannelMapper 
{
    /**
     * 查询渠道方数据集合
     * 
     * @param channel 渠道方信息
     * @return 渠道方数据集合
     */
    public List<SysChannel> selectChannelList(SysChannel channel);

    /**
     * 查询所有渠道方
     * 
     * @return 渠道方列表
     */
    public List<SysChannel> selectChannelAll();
    
    /**
     * 通过渠道ID查询渠道方信息
     * 
     * @param channelId 渠道ID
     * @return 渠道方信息
     */
    public SysChannel selectChannelById(Long channelId);
    
    /**
     * 通过渠道名称查询渠道方信息
     * 
     * @param channelName 渠道名称
     * @return 渠道方信息
     */
    public SysChannel selectChannelByName(String channelName);
    
    /**
     * 新增渠道方信息
     * 
     * @param channel 渠道方信息
     * @return 结果
     */
    public int insertChannel(SysChannel channel);
    
    /**
     * 修改渠道方信息
     * 
     * @param channel 渠道方信息
     * @return 结果
     */
    public int updateChannel(SysChannel channel);
    
    /**
     * 删除渠道方信息
     * 
     * @param channelId 渠道ID
     * @return 结果
     */
    public int deleteChannelById(Long channelId);
    
    /**
     * 批量删除渠道方信息
     * 
     * @param channelIds 需要删除的渠道ID
     * @return 结果
     */
    public int deleteChannelByIds(Long[] channelIds);
}
