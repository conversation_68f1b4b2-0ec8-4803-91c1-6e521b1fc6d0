package com.ruoyi.web.controller.system;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.SysChannel;
import com.ruoyi.system.service.ISysChannelService;

/**
 * 渠道方信息
 * 
 */
@Controller
@RequestMapping("/system/channel")
public class SysChannelController extends BaseController
{
    private String prefix = "system/channel";

    @Autowired
    private ISysChannelService channelService;

    @RequiresPermissions("system:channel:view")
    @GetMapping()
    public String channel()
    {
        return prefix + "/channel";
    }

    @RequiresPermissions("system:channel:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SysChannel channel)
    {
        startPage();
        List<SysChannel> list = channelService.selectChannelList(channel);
        return getDataTable(list);
    }

    @RequiresPermissions("system:channel:export")
    @Log(title = "渠道方管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SysChannel channel)
    {
        List<SysChannel> list = channelService.selectChannelList(channel);
        ExcelUtil<SysChannel> util = new ExcelUtil<SysChannel>(SysChannel.class);
        return util.exportExcel(list, "渠道方数据");
    }

    /**
     * 新增渠道方
     */
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存渠道方
     */
    @RequiresPermissions("system:channel:add")
    @Log(title = "渠道方管理", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@Validated SysChannel channel)
    {
        if (UserConstants.CHANNEL_NAME_NOT_UNIQUE.equals(channelService.checkChannelNameUnique(channel)))
        {
            return error("新增渠道'" + channel.getChannelName() + "'失败，渠道名称已存在");
        }
        return toAjax(channelService.insertChannel(channel));
    }

    /**
     * 修改渠道方
     */
    @RequiresPermissions("system:channel:edit")
    @GetMapping("/edit/{channelId}")
    public String edit(@PathVariable("channelId") Long channelId, ModelMap mmap)
    {
        mmap.put("channel", channelService.selectChannelById(channelId));
        return prefix + "/edit";
    }

    /**
     * 修改保存渠道方
     */
    @RequiresPermissions("system:channel:edit")
    @Log(title = "渠道方管理", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@Validated SysChannel channel)
    {
        if (UserConstants.CHANNEL_NAME_NOT_UNIQUE.equals(channelService.checkChannelNameUnique(channel)))
        {
            return error("修改渠道'" + channel.getChannelName() + "'失败，渠道名称已存在");
        }
        return toAjax(channelService.updateChannel(channel));
    }

    /**
     * 删除渠道方
     */
    @RequiresPermissions("system:channel:remove")
    @Log(title = "渠道方管理", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(channelService.deleteChannelByIds(Convert.toLongArray(ids)));
    }

    /**
     * 校验渠道名称
     */
    @PostMapping("/checkChannelNameUnique")
    @ResponseBody
    public String checkChannelNameUnique(SysChannel channel)
    {
        return channelService.checkChannelNameUnique(channel);
    }
    
    /**
     * 获取渠道选择框列表
     */
    @GetMapping("/channelList")
    @ResponseBody
    public AjaxResult channelList()
    {
        List<SysChannel> list = channelService.selectChannelAll();
        return AjaxResult.success(list);
    }
}
