package com.ruoyi.system.mapper;

import com.ruoyi.system.domain.MediationCase;
import java.util.List;

public interface MediationCaseMapper {
    MediationCase selectMediationCaseById(Long id);
    List<MediationCase> selectMediationCaseList(MediationCase mediationCase);
    int insertMediationCase(MediationCase mediationCase);
    int updateMediationCase(MediationCase mediationCase);
    int deleteMediationCaseById(Long id);
    int deleteMediationCaseByIds(Long[] ids);
}