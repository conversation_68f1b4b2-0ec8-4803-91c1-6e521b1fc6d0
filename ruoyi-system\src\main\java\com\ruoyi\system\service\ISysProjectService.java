package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.SysProject;

/**
 * 项目管理 服务层
 * 
 */
public interface ISysProjectService 
{
    /**
     * 查询项目数据集合
     * 
     * @param project 项目信息
     * @return 项目数据集合
     */
    public List<SysProject> selectProjectList(SysProject project);

    /**
     * 查询所有项目
     * 
     * @return 项目列表
     */
    public List<SysProject> selectProjectAll();
    
    /**
     * 通过项目ID查询项目信息
     * 
     * @param projectId 项目ID
     * @return 项目信息
     */
    public SysProject selectProjectById(Long projectId);
    
    /**
     * 校验项目名称是否唯一
     * 
     * @param project 项目信息
     * @return 结果
     */
    public String checkProjectNameUnique(SysProject project);
    
    /**
     * 新增项目信息
     * 
     * @param project 项目信息
     * @return 结果
     */
    public int insertProject(SysProject project);
    
    /**
     * 修改项目信息
     * 
     * @param project 项目信息
     * @return 结果
     */
    public int updateProject(SysProject project);
    
    /**
     * 删除项目信息
     * 
     * @param projectId 项目ID
     * @return 结果
     */
    public int deleteProjectById(Long projectId);
    
    /**
     * 批量删除项目信息
     * 
     * @param projectIds 需要删除的项目ID
     * @return 结果
     */
    public int deleteProjectByIds(Long[] projectIds);
}
