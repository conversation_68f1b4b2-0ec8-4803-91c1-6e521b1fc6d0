package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.SysProject;
import com.ruoyi.system.mapper.SysProjectMapper;
import com.ruoyi.system.service.ISysProjectService;

/**
 * 项目管理 服务层实现
 * 
 */
@Service
public class SysProjectServiceImpl implements ISysProjectService 
{
    @Autowired
    private SysProjectMapper projectMapper;

    /**
     * 查询项目数据集合
     * 
     * @param project 项目信息
     * @return 项目数据集合
     */
    @Override
    public List<SysProject> selectProjectList(SysProject project)
    {
        return projectMapper.selectProjectList(project);
    }

    /**
     * 查询所有项目
     * 
     * @return 项目列表
     */
    @Override
    public List<SysProject> selectProjectAll()
    {
        return projectMapper.selectProjectAll();
    }

    /**
     * 通过项目ID查询项目信息
     * 
     * @param projectId 项目ID
     * @return 项目信息
     */
    @Override
    public SysProject selectProjectById(Long projectId)
    {
        return projectMapper.selectProjectById(projectId);
    }

    /**
     * 校验项目名称是否唯一
     * 
     * @param project 项目信息
     * @return 结果
     */
    @Override
    public String checkProjectNameUnique(SysProject project)
    {
        Long projectId = StringUtils.isNull(project.getProjectId()) ? -1L : project.getProjectId();
        SysProject info = projectMapper.selectProjectByName(project.getProjectName());
        if (StringUtils.isNotNull(info) && info.getProjectId().longValue() != projectId.longValue())
        {
            return UserConstants.PROJECT_NAME_NOT_UNIQUE;
        }
        return UserConstants.PROJECT_NAME_UNIQUE;
    }

    /**
     * 新增项目信息
     * 
     * @param project 项目信息
     * @return 结果
     */
    @Override
    public int insertProject(SysProject project)
    {
        return projectMapper.insertProject(project);
    }

    /**
     * 修改项目信息
     * 
     * @param project 项目信息
     * @return 结果
     */
    @Override
    public int updateProject(SysProject project)
    {
        return projectMapper.updateProject(project);
    }

    /**
     * 删除项目信息
     * 
     * @param projectId 项目ID
     * @return 结果
     */
    @Override
    public int deleteProjectById(Long projectId)
    {
        return projectMapper.deleteProjectById(projectId);
    }

    /**
     * 批量删除项目信息
     * 
     * @param projectIds 需要删除的项目ID
     * @return 结果
     */
    @Override
    public int deleteProjectByIds(Long[] projectIds)
    {
        return projectMapper.deleteProjectByIds(projectIds);
    }
}
