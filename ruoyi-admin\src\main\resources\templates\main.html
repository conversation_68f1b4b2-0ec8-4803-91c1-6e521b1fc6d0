<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调解管理系统</title>
    <link rel="shortcut icon" href="favicon.ico">
    <link href="/css/bootstrap.min.css" rel="stylesheet"/>
    <link href="/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/css/style.min.css" rel="stylesheet"/>
    <link href="/ajax/libs/select2/select2.min.css" rel="stylesheet"/>    <style>
        .main-search-bar { margin: 20px 0 30px 0; }
        .main-search-bar .form-group { margin-right: 18px; }
        .main-search-bar .form-inline { display: flex; flex-wrap: wrap; align-items: center; }
        .main-search-bar .form-control { min-width: 160px; }
        .main-search-bar .select2-container { min-width: 160px; }
        .main-search-bar .btn { margin-left: 10px; }
        .main-stat-card { background: #fff; border-radius: 6px; padding: 30px 0 30px 0; margin-bottom: 20px; }
        .main-stat-title { font-size: 22px; font-weight: bold; margin-bottom: 18px; }
        .main-stat-row { display: flex; justify-content: flex-start; }
        .main-stat-item { flex: 1; text-align: left; }
        .main-stat-label { font-size: 16px; color: #333; margin-bottom: 6px; }
        .main-stat-value { font-size: 24px; font-weight: bold; color: #222; }
        @media (max-width: 900px) {
            .main-stat-row { flex-direction: column; }
            .main-stat-item { margin-bottom: 18px; }
        }
    </style>
</head>
<body class="gray-bg">
<div class="container-fluid">    <div class="main-search-bar">
        <form class="form-inline" id="mainSearchForm">
            <div class="form-group">
                <label>公司</label>
                <select class="form-control deptSelectpicker" name="deptId" data-placeholder="请选择公司"></select>
            </div>
            <div class="form-group">
                <label>管理员</label>
                <select class="form-control adminSelectpicker" name="adminId" data-placeholder="请选择管理员"></select>
            </div>
            <div class="form-group">
                <label>渠道方</label>
                <select class="form-control channelSelectpicker" name="channelId" data-placeholder="请选择渠道方"></select>
            </div>
            <div class="form-group">
                <label>调解员</label>
                <select class="form-control mediatorSelectpicker" name="mediatorId" data-placeholder="请选择调解员"></select>
            </div>
            <button type="button" class="btn btn-primary" id="searchBtn"><i class="fa fa-search"></i> 搜索</button>
        </form>
    </div>
    <div class="main-stat-card" style="padding: 20px;">
        <div class="main-stat-title">案件数据</div>
        <div class="main-stat-row">            <div class="main-stat-item">
                <div class="main-stat-label">案件总数</div>
                <div class="main-stat-value" id="totalCount" th:text="${caseStats != null ? caseStats.totalCount : 0}">0</div>
            </div>
            <div class="main-stat-item">
                <div class="main-stat-label">处理中</div>
                <div class="main-stat-value" id="processingCount" th:text="${caseStats != null ? caseStats.processingCount : 0}">0</div>
            </div>
            <div class="main-stat-item">
                <div class="main-stat-label">已确认</div>
                <div class="main-stat-value" id="confirmedCount" th:text="${caseStats != null ? caseStats.confirmedCount : 0}">0</div>
            </div>
        </div>    </div>
</div>

<script src="/js/jquery.min.js"></script>
<script src="/js/bootstrap.min.js"></script>
<script src="/ajax/libs/select2/select2.min.js"></script>
<script>
$(function() {
    // 初始化下拉选择框
    loadDeptData();
    loadAdminData();
    loadMediatorData();
    loadChannelData();
    
    // 绑定搜索按钮事件
    $("#searchBtn").click(function() {
        searchCaseStatistics();
    });
    
    // 搜索案件统计函数
    function searchCaseStatistics() {
        var formData = {
            deptId: $(".deptSelectpicker").val(),
            adminId: $(".adminSelectpicker").val(),
            channelId: $(".channelSelectpicker").val(),
            mediatorId: $(".mediatorSelectpicker").val()
        };
        
        // 过滤掉空值
        Object.keys(formData).forEach(function(key) {
            if (!formData[key]) {
                delete formData[key];
            }
        });
        
        $.ajax({
            type: "POST",
            url: "/system/getCaseStatistics",
            data: formData,
            success: function(result) {
                if (result.code == 0) {
                    // 更新统计数据
                    var data = result.data;
                    $("#totalCount").text(data.totalCount || 0);
                    $("#processingCount").text(data.processingCount || 0);
                    $("#confirmedCount").text(data.confirmedCount || 0);
                } else {
                    alert("获取统计数据失败：" + result.msg);
                }
            },
            error: function() {
                alert("获取统计数据失败，请稍后再试");
            }
        });    }
    
    /* 加载部门数据 */
    function loadDeptData() {
        var deptTreeSelect = $(".deptSelectpicker");
        var prefix = "/mediation/case"; // 使用案件管理的API前缀
        $.ajax({
            type: "GET",
            url: prefix + "/deptTree",
            dataType: "json",
            success: function(data) {
                deptTreeSelect.empty();
                deptTreeSelect.append("<option value=''>请选择公司</option>");
                $.each(data, function(index, dept) {
                    deptTreeSelect.append("<option value='" + dept.id + "'>" + dept.name + "</option>");
                });
                deptTreeSelect.select2({
                    placeholder: "请选择公司",
                    allowClear: true
                });
            }
        });
    }
    
    /* 加载管理员数据 */
    function loadAdminData() {
        var adminSelect = $(".adminSelectpicker");
        var prefix = "/mediation/case"; // 使用案件管理的API前缀
        $.ajax({
            type: "GET",
            url: prefix + "/adminUsers",
            dataType: "json",
            success: function(res) {
                if (res.code == 0) {
                    var data = res.data;
                    adminSelect.empty();
                    adminSelect.append("<option value=''>请选择管理员</option>");
                    $.each(data, function(index, user) {
                        adminSelect.append("<option value='" + user.userId + "'>" + user.userName + "</option>");
                    });
                    adminSelect.select2({
                        placeholder: "请选择管理员",
                        allowClear: true
                    });
                }
            }
        });
    }
    
    /* 加载调解员数据 */
    function loadMediatorData() {
        var mediatorSelect = $(".mediatorSelectpicker");
        var prefix = "/mediation/case"; // 使用案件管理的API前缀
        $.ajax({
            type: "GET",
            url: prefix + "/mediatorUsers",
            dataType: "json",
            success: function(res) {
                if (res.code == 0) {
                    var data = res.data;
                    mediatorSelect.empty();
                    mediatorSelect.append("<option value=''>请选择调解员</option>");
                    $.each(data, function(index, user) {
                        mediatorSelect.append("<option value='" + user.userId + "'>" + user.userName + "</option>");
                    });
                    mediatorSelect.select2({
                        placeholder: "请选择调解员",
                        allowClear: true
                    });
                }
            }
        });
    }
    
    /* 加载渠道方数据 */
    function loadChannelData() {
        var channelSelect = $(".channelSelectpicker");
        var prefix = "/mediation/case"; // 使用案件管理的API前缀
        $.ajax({
            type: "GET",
            url: prefix + "/channelList",
            dataType: "json",
            success: function(res) {
                if (res.code == 0) {
                    var data = res.data;
                    channelSelect.empty();
                    channelSelect.append("<option value=''>请选择渠道方</option>");
                    $.each(data, function(index, channel) {
                        channelSelect.append("<option value='" + channel.channelId + "'>" + channel.channelName + "</option>");
                    });
                    channelSelect.select2({
                        placeholder: "请选择渠道方",
                        allowClear: true
                    });
                }
            }
        });
    }
});
</script>
</body>
</html>
