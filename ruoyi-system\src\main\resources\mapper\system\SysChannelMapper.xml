<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysChannelMapper">

    <resultMap type="SysChannel" id="SysChannelResult">
        <id     property="channelId"      column="channel_id"      />
        <result property="channelName"    column="channel_name"    />
        <result property="status"         column="status"          />
        <result property="createBy"       column="create_by"       />
        <result property="createTime"     column="create_time"     />
        <result property="updateBy"       column="update_by"       />
        <result property="updateTime"     column="update_time"     />
        <result property="remark"         column="remark"          />
    </resultMap>
    
    <sql id="selectChannelVo">
        select channel_id, channel_name, status, create_by, create_time, update_by, update_time, remark
        from sys_channel
    </sql>
    
    <select id="selectChannelList" parameterType="SysChannel" resultMap="SysChannelResult">
        <include refid="selectChannelVo"/>
        <where>
            <if test="channelName != null and channelName != ''">
                AND channel_name like concat('%', #{channelName}, '%')
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
        </where>
    </select>
    
    <select id="selectChannelAll" resultMap="SysChannelResult">
        <include refid="selectChannelVo"/>
    </select>
    
    <select id="selectChannelById" parameterType="Long" resultMap="SysChannelResult">
        <include refid="selectChannelVo"/>
        where channel_id = #{channelId}
    </select>
    
    <select id="selectChannelByName" parameterType="String" resultMap="SysChannelResult">
        <include refid="selectChannelVo"/>
        where channel_name = #{channelName}
    </select>
    
    <insert id="insertChannel" parameterType="SysChannel" useGeneratedKeys="true" keyProperty="channelId">
        insert into sys_channel(
            <if test="channelName != null and channelName != ''">channel_name,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            create_time
        )values(
            <if test="channelName != null and channelName != ''">#{channelName},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            sysdate()
        )
    </insert>
    
    <update id="updateChannel" parameterType="SysChannel">
        update sys_channel
        <set>
            <if test="channelName != null and channelName != ''">channel_name = #{channelName},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        where channel_id = #{channelId}
    </update>
    
    <delete id="deleteChannelById" parameterType="Long">
        delete from sys_channel where channel_id = #{channelId}
    </delete>
    
    <delete id="deleteChannelByIds" parameterType="Long">
        delete from sys_channel where channel_id in 
        <foreach collection="array" item="channelId" open="(" separator="," close=")">
            #{channelId}
        </foreach>
    </delete>
    
</mapper>
