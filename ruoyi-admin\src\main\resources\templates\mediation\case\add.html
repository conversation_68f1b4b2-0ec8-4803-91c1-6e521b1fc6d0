<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
	<th:block th:include="include :: header('新增案件')" />
	<th:block th:include="include :: select2-css" />
</head>
<body class="white-bg">
	<div class="wrapper wrapper-content animated fadeInRight ibox-content">
		<form class="form-horizontal m" id="form-case-add">					<div class="form-group">
				<label class="col-sm-3 control-label is-required">公司：</label>
				<div class="col-sm-8">
					<select class="form-control deptSelectpicker" name="deptId" required data-placeholder="请选择公司"></select>
				</div>
			</div>			<div class="form-group">
				<label class="col-sm-3 control-label is-required">管理员：</label>
				<div class="col-sm-8">
					<select class="form-control adminSelectpicker" name="adminId" required data-placeholder="请选择管理员"></select>
				</div>
			</div>			<div class="form-group">
				<label class="col-sm-3 control-label">渠道方：</label>
				<div class="col-sm-8">
					<select class="form-control channelSelectpicker" name="channelId" data-placeholder="请选择渠道方"></select>
				</div>
			</div>			<div class="form-group">
				<label class="col-sm-3 control-label">项目：</label>
				<div class="col-sm-8">
					<select class="form-control projectSelectpicker" name="projectId" data-placeholder="请选择项目"></select>
				</div>
			</div>			<div class="form-group">
				<label class="col-sm-3 control-label">调解员：</label>
				<div class="col-sm-8">
					<select class="form-control mediatorSelectpicker" name="mediatorId" data-placeholder="请选择调解员"></select>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">日期：</label>
				<div class="col-sm-8">
					<input class="form-control" type="date" name="caseDate" placeholder="请选择日期">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">原告：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="plaintiff" placeholder="请输入原告信息">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">被告：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="defendant" placeholder="请输入被告信息">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">电话：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="phone" placeholder="请输入联系电话">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">一刷备注：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="remark1" placeholder="请输入一刷备注">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">二刷备注：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="remark2" placeholder="请输入二刷备注">
				</div>
			</div>			<div class="form-group">
				<label class="col-sm-3 control-label">三刷备注：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="remark3" placeholder="请输入三刷备注">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">案件状态：</label>
				<div class="col-sm-8">
					<select name="status" class="form-control">
						<option value="1">处理中</option>
						<option value="2">处理失败</option>
						<option value="3">处理成功</option>
						<option value="4">已确认</option>
					</select>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">处理成功日期：</label>
				<div class="col-sm-8">
					<input class="form-control" type="date" name="successDate" placeholder="请选择处理成功日期">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">项目名称：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="projectName" placeholder="请输入项目名称">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">备注：</label>
				<div class="col-sm-8">
					<textarea class="form-control" name="note" placeholder="请输入备注" rows="3"></textarea>
				</div>
			</div>
		</form>
	</div>
	<th:block th:include="include :: footer" />
	<th:block th:include="include :: select2-js" />	<script th:inline="javascript">
		var prefix = ctx + "mediation/case";
		
		$(function() {
			// 加载公司数据
			loadDeptData();
			// 加载管理员数据
			loadAdminData();
			// 加载调解员数据
			loadMediatorData();
			// 加载渠道方数据
			loadChannelData();
			// 加载项目数据
			loadProjectData();
		});
				/* 加载公司数据 */
		function loadDeptData() {
			var deptSelect = $(".deptSelectpicker");			$.ajax({
				url: prefix + "/deptTree",
				type: "get",
				dataType: "json",
				success: function(data) {
					deptSelect.empty();
					deptSelect.append("<option value=''>请选择公司</option>");					$.each(data, function(index, dept) {
						deptSelect.append("<option value='" + dept.id + "'>" + dept.name + "</option>");
					});
					deptSelect.select2({
						placeholder: "请选择公司",
						allowClear: true
					});
				}
			});
		}
		
		/* 加载管理员数据 */
		function loadAdminData() {
			var adminSelect = $(".adminSelectpicker");			$.ajax({
				type: "GET",
				url: prefix + "/adminUsers",
				dataType: "json",
				success: function(res) {
					if (res.code == 0) {
						var data = res.data;
						adminSelect.empty();
						adminSelect.append("<option value=''>请选择管理员</option>");						$.each(data, function(index, user) {
							adminSelect.append("<option value='" + user.userId + "'>" + user.userName + "</option>");
						});
						adminSelect.select2({
							placeholder: "请选择管理员",
							allowClear: true
						});
					}
				}
			});
		}
		
		/* 加载调解员数据 */
		function loadMediatorData() {
			var mediatorSelect = $(".mediatorSelectpicker");			$.ajax({
				type: "GET",
				url: prefix + "/mediatorUsers",
				dataType: "json",
				success: function(res) {
					if (res.code == 0) {
						var data = res.data;
						mediatorSelect.empty();
						mediatorSelect.append("<option value=''>请选择调解员</option>");						$.each(data, function(index, user) {
							mediatorSelect.append("<option value='" + user.userId + "'>" + user.userName + "</option>");
						});
						mediatorSelect.select2({
							placeholder: "请选择调解员",
							allowClear: true
						});
					}
				}
			});
		}
		
		/* 加载渠道方数据 */
		function loadChannelData() {
			var channelSelect = $(".channelSelectpicker");			$.ajax({
				type: "GET",
				url: prefix + "/channelList",
				dataType: "json",
				success: function(res) {
					if (res.code == 0) {
						var data = res.data;
						channelSelect.empty();
						channelSelect.append("<option value=''>请选择渠道方</option>");						$.each(data, function(index, channel) {
							channelSelect.append("<option value='" + channel.channelId + "'>" + channel.channelName + "</option>");
						});
						channelSelect.select2({
							placeholder: "请选择渠道方",
							allowClear: true
						});
					}
				}
			});
		}
		
		/* 加载项目数据 */
		function loadProjectData() {
			var projectSelect = $(".projectSelectpicker");			$.ajax({
				type: "GET",
				url: prefix + "/projectList",
				dataType: "json",
				success: function(res) {
					if (res.code == 0) {
						var data = res.data;
						projectSelect.empty();
						projectSelect.append("<option value=''>请选择项目</option>");						$.each(data, function(index, project) {
							projectSelect.append("<option value='" + project.projectId + "'>" + project.projectName + "</option>");
						});
						projectSelect.select2({
							placeholder: "请选择项目",
							allowClear: true
						});
					}
				}
			});
		}		function submitHandler() {
			// 添加额外验证
			var company = $(".deptSelectpicker").val();
			if (!company) {
				$.modal.alertWarning("请选择公司");
				return;
			}
			
			var admin = $(".adminSelectpicker").val();
			if (!admin) {
				$.modal.alertWarning("请选择管理员");
				return;
			}
			
			if ($.validate.form()) {
				$.operate.save(prefix + "/add", $('#form-case-add').serialize());
			}
		}
	</script>
</body>
</html>
