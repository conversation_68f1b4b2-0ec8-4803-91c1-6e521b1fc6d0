<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.MediationCaseMapper">    <resultMap id="MediationCaseResultMap" type="com.ruoyi.system.domain.MediationCase">
        <id property="id" column="id" />
        <result property="deptId" column="dept_id" />
        <result property="adminId" column="admin_id" />
        <result property="channelId" column="channel_id" />
        <result property="projectId" column="project_id" />
        <result property="mediatorId" column="mediator_id" />
        <result property="caseDate" column="case_date" />
        <result property="plaintiff" column="plaintiff" />
        <result property="defendant" column="defendant" />
        <result property="phone" column="phone" />
        <result property="remark1" column="remark1" />
        <result property="remark2" column="remark2" />
        <result property="remark3" column="remark3" />
        <result property="amount" column="amount" />
        <result property="voucher" column="voucher" />
        <result property="status" column="status" />
        <result property="successDate" column="success_date" />
        <result property="projectName" column="project_name" />
        <result property="note" column="note" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
    </resultMap>    <select id="selectMediationCaseById" resultMap="MediationCaseResultMap">
        SELECT mc.*, 
               d.dept_name as companyName,
               u1.user_name as adminName,
               u2.user_name as mediatorName,
               c.channel_name as channelName
        FROM mediation_case mc
        LEFT JOIN sys_dept d ON mc.dept_id = d.dept_id
        LEFT JOIN sys_user u1 ON mc.admin_id = u1.user_id
        LEFT JOIN sys_user u2 ON mc.mediator_id = u2.user_id
        LEFT JOIN sys_channel c ON mc.channel_id = c.channel_id
        WHERE mc.id = #{id}
    </select><select id="selectMediationCaseList" resultMap="MediationCaseResultMap">
        SELECT mc.*, 
               d.dept_name as companyName,
               u1.user_name as adminName,
               u2.user_name as mediatorName,
               c.channel_name as channelName
        FROM mediation_case mc
        LEFT JOIN sys_dept d ON mc.dept_id = d.dept_id
        LEFT JOIN sys_user u1 ON mc.admin_id = u1.user_id
        LEFT JOIN sys_user u2 ON mc.mediator_id = u2.user_id
        LEFT JOIN sys_channel c ON mc.channel_id = c.channel_id
        <where>
            <if test="deptId != null">AND mc.dept_id = #{deptId}</if>
            <if test="adminId != null">AND mc.admin_id = #{adminId}</if>
            <if test="channelId != null">AND mc.channel_id = #{channelId}</if>
            <if test="projectId != null">AND mc.project_id = #{projectId}</if>
            <if test="mediatorId != null">AND mc.mediator_id = #{mediatorId}</if>
            <if test="caseDate != null">AND mc.case_date = #{caseDate}</if>
            <if test="plaintiff != null and plaintiff != ''">AND mc.plaintiff LIKE concat('%', #{plaintiff}, '%')</if>
            <if test="defendant != null and defendant != ''">AND mc.defendant LIKE concat('%', #{defendant}, '%')</if>
            <if test="phone != null and phone != ''">AND mc.phone = #{phone}</if>
            <if test="status != null and status != ''">AND mc.status = #{status}</if>
            <if test="projectName != null and projectName != ''">AND mc.project_name LIKE concat('%', #{projectName}, '%')</if>
            <if test="successDate != null">AND mc.success_date = #{successDate}</if>
        </where>
        ORDER BY mc.id DESC
    </select>    <insert id="insertMediationCase" parameterType="com.ruoyi.system.domain.MediationCase">
        INSERT INTO mediation_case(
            dept_id, admin_id, channel_id, project_id, mediator_id, case_date, plaintiff, defendant, phone, 
            remark1, remark2, remark3, status, success_date, project_name, note, create_time, update_time
        ) VALUES (
            #{deptId}, #{adminId}, #{channelId}, #{projectId}, #{mediatorId}, #{caseDate}, #{plaintiff}, #{defendant}, #{phone}, 
            #{remark1}, #{remark2}, #{remark3}, #{status}, #{successDate}, #{projectName}, #{note}, NOW(), NOW()
        )
    </insert><update id="updateMediationCase" parameterType="com.ruoyi.system.domain.MediationCase">
        UPDATE mediation_case SET
            dept_id = #{deptId},
            admin_id = #{adminId},
            channel_id = #{channelId},
            project_id = #{projectId},
            mediator_id = #{mediatorId},
            case_date = #{caseDate},
            plaintiff = #{plaintiff},
            defendant = #{defendant},
            phone = #{phone},
            remark1 = #{remark1},
            remark2 = #{remark2},
            remark3 = #{remark3},
            status = #{status},
            success_date = #{successDate},
            project_name = #{projectName},
            note = #{note},
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <delete id="deleteMediationCaseById" parameterType="long">
        DELETE FROM mediation_case WHERE id = #{id}
    </delete>

    <delete id="deleteMediationCaseByIds" parameterType="long">
        DELETE FROM mediation_case WHERE id IN
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 