<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('案件管理')" />
    <th:block th:include="include :: layout-latest-css" />
    <th:block th:include="include :: select2-css" />    <th:block th:include="include :: bootstrap-select-css" />
    <style>
        .case-search-btns {
            margin-top: 18px;
            margin-left: 0;
            text-align: left;
        }
        .case-search-btns .btn {
            font-size: 18px;
            padding: 8px 28px;
            border-radius: 24px;
            margin-right: 12px;
        }
        .nowrap-label {
            white-space: nowrap;
            display: inline-block;
        }
        .case-modal .modal-dialog {
            max-width: 480px;
        }
        .case-modal .modal-body {
            padding: 30px 30px 10px 30px;
        }
        .case-modal .form-group {
            margin-bottom: 18px;
        }
        .case-modal .form-control {
            border-radius: 4px;
        }
        .case-modal .modal-footer {
            padding: 18px 30px 18px 30px;
        }
        .case-modal .modal-title {
            font-size: 20px;
            font-weight: bold;
        }
        .case-modal label {
            font-weight: bold;
        }
        .case-modal-input {
            height: 48px;
            border-radius: 8px;
            border: 1px solid #e5e6eb;
            font-size: 18px;
            color: #222;
            background: #fafbfc;
            box-shadow: none;
            padding-left: 16px;
            padding-right: 16px;
            transition: border-color 0.2s;
        }
        .case-modal-input:focus {
            border-color: #409eff;
            background: #fff;
            outline: none;
        }
        .case-modal-label {
            font-size: 18px;
            color: #222;
            font-weight: 600;
            margin-bottom: 8px;
            display: block;
        }
        .case-modal-form-row {
            margin-bottom: 28px;
        }
        .case-modal-select {
            appearance: none;
            -webkit-appearance: none;
            background: #fafbfc url('data:image/svg+xml;utf8,<svg fill="%23999" height="20" viewBox="0 0 20 20" width="20" xmlns="http://www.w3.org/2000/svg"><path d="M7.293 7.293a1 1 0 011.414 0L10 8.586l1.293-1.293a1 1 0 111.414 1.414l-2 2a1 1 0 01-1.414 0l-2-2a1 1 0 010-1.414z"/></svg>') no-repeat right 16px center/18px 18px;
            padding-right: 40px;
        }
        .case-modal-btn {
            height: 44px;
            min-width: 100px;
            font-size: 18px;
            border-radius: 8px;
        }
        /* 添加搜索区的布局样式 */
        .search-collapse .select-list ul {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            margin-bottom: 10px;
        }
        .search-collapse .select-list li {
            display: flex;
            align-items: center;
            margin-right: 15px;
            flex: 0 0 auto;
            white-space: nowrap;
        }
        .search-collapse .select-list input,
        .search-collapse .select-list select {
            width: 180px;
            margin-left: 5px;
        }
        .search-collapse .select-list .select-time input {
            width: 120px;
        }
        .search-collapse .select-list .select-time span {
            margin: 0 5px;
        }
    </style>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">            <form id="case-form">                <div class="select-list">                    <!-- 第一行 -->
                    <ul>
                        <li>公司：
                            <select name="company" class="form-control deptSelectpicker" data-placeholder="请选择公司"></select>
                        </li>                        <li>管理员：
                            <select name="admin" class="form-control adminSelectpicker" data-placeholder="请选择管理员"></select>
                        </li>                        <li>渠道方：
                            <select name="channel" class="form-control channelSelectpicker" data-placeholder="请选择渠道方"></select>
                        </li>
                        <li>项目：
                            <select name="project" class="form-control projectSelectpicker" data-placeholder="请选择项目"></select>
                        </li><li>调解员：
                            <select name="mediator" class="form-control mediatorSelectpicker" data-placeholder="请选择调解员"></select>
                        </li>
                    </ul>
                    <!-- 第二行 -->
                    <ul>
                        <li class="select-time">
                            <label>日期范围：</label>
                            <input type="text" class="time-input" id="startDate" placeholder="开始日期" name="params[beginDate]"/>
                            <span>-</span>
                            <input type="text" class="time-input" id="endDate" placeholder="结束日期" name="params[endDate]"/>
                        </li>
                        <li>原告：<input type="text" name="plaintiff" placeholder="请输入原告信息"/></li>
                        <li>被告：<input type="text" name="defendant" placeholder="请输入被告信息"/></li>
                        <li>电话：<input type="text" name="phone" placeholder="请输入联系电话"/></li>
                    </ul>
                    <!-- 第三行 -->
                    <ul>
                        <li>案件状态：
                            <select name="status" class="form-control">
                                <option value="">请选择案件状态</option>
                                <option value="1">处理中</option>
                                <option value="2">处理失败</option>
                                <option value="3">处理成功</option>
                                <option value="4">已确认</option>
                            </select>
                        </li>
                        <li>备注：<input type="text" name="note" placeholder="请输入备注"/></li>
                        <li>项目名称查询：<input type="text" name="projectName" placeholder="请输入项目名称"/></li>
                    </ul>
                    <!-- 第四行 -->
                    <ul>
                        <li class="select-time">
                            处理成功日期：
                            <input type="text" class="time-input" id="successStartDate" placeholder="开始日期" name="params[successBeginDate]"/>
                            <span>-</span>
                            <input type="text" class="time-input" id="successEndDate" placeholder="结束日期" name="params[successEndDate]"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>&nbsp;&nbsp;
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="$.operate.add()"><i class="fa fa-plus"></i> 新增</a>
            <a class="btn btn-info" onclick="$.table.importExcel()"><i class="fa fa-upload"></i> 批量上传案件</a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()"><i class="fa fa-download"></i> 导出</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>    </div>
</div>


<th:block th:include="include :: footer" />
<th:block th:include="include :: layout-latest-js" />
<th:block th:include="include :: select2-js" />
<th:block th:include="include :: bootstrap-select-js" />
<th:block th:include="include :: bootstrap-datetimepicker-js" />
<script th:inline="javascript">
var editFlag = [[${@permission.hasPermi('mediation:case:edit')}]];
var removeFlag = [[${@permission.hasPermi('mediation:case:remove')}]];
var prefix = ctx + "mediation/case";

$(function() {        var options = {
        url: prefix + "/list",
        createUrl: prefix + "/add",
        updateUrl: prefix + "/edit/{id}",
        removeUrl: prefix + "/remove/{id}", // 修正了removeUrl路径
        exportUrl: prefix + "/export",
        importUrl: prefix + "/importData",
        sortName: "createTime",
        sortOrder: "desc",
        modalName: "案件",            columns: [
                { checkbox: true },
                // { field: 'companyName', title: '公司' },
                { field: 'adminName', title: '管理员' },
                { field: 'channelName', title: '渠道方' },
                { field: 'projectName', title: '项目' },
                { field: 'mediatorName', title: '调解员' },
                { field: 'caseDate', title: '日期' },
                { field: 'plaintiff', title: '原告' },
                { field: 'defendant', title: '被告' },
                { field: 'phone', title: '电话' },
                { field: 'remark1', title: '一刷备注' },
                { field: 'remark2', title: '二刷备注' },
                { field: 'remark3', title: '三刷备注' },
                { field: 'amount', title: '回款金额' },
                { field: 'voucher', title: '回款凭证' },
                { field: 'status', title: '案件状态' ,
                    formatter: function(value, row, index) {
                        var rs = ''
                        if(value === '1') {
                            rs = '<span>处理中</span>'
                        } else if(value === '2') {
                            rs = '<span>处理失败</span>'
                        } else if(value === '3') {
                            rs = '<span>处理成功</span>'
                        } else {
                            rs = '<span>已确认</span>'
                        }
                        return rs;
                    }
                },   
                { field: 'successDate', title: '处理成功日期' },             
                { title: '操作', align: 'center',
                  formatter: function(value, row, index) {
                      var actions = [];
                      actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                      actions.push('<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a> ');
                      return actions.join('');
                  }
                }
            ]
        };        $.table.init(options);
        
        // 初始化部门下拉框
        loadDeptData();
        // 加载管理员数据
        loadAdminData();
        // 加载调解员数据
        loadMediatorData();
        // 加载渠道方数据
        loadChannelData();
        // 加载项目数据
        loadProjectData();
    });
      
    /* 加载部门数据 */
    function loadDeptData() {
        var deptTreeSelect = $(".deptSelectpicker");
        $.ajax({
            type: "GET",
            url: prefix + "/deptTree",
            dataType: "json",
            success: function(data) {
                deptTreeSelect.empty();
                deptTreeSelect.append("<option value=''>请选择公司</option>");
                $.each(data, function(index, dept) {
                    deptTreeSelect.append("<option value='" + dept.name + "'>" + dept.name + "</option>");
                });
                deptTreeSelect.select2({
                    placeholder: "请选择公司",
                    allowClear: true
                });
            }
        });
        
        // 加载管理员数据
        loadAdminData();
        
        // 加载调解员数据
        loadMediatorData();
    }
    
    /* 加载管理员数据 */
    function loadAdminData() {
        var adminSelect = $(".adminSelectpicker");
        $.ajax({
            type: "GET",
            url: prefix + "/adminUsers",
            dataType: "json",
            success: function(res) {
                if (res.code == 0) {
                    var data = res.data;
                    adminSelect.empty();
                    adminSelect.append("<option value=''>请选择管理员</option>");
                    $.each(data, function(index, user) {
                        adminSelect.append("<option value='" + user.userName + "'>" + user.userName + "</option>");
                    });
                    adminSelect.select2({
                        placeholder: "请选择管理员",
                        allowClear: true
                    });
                }
            }
        });
    }
    
    /* 加载调解员数据 */
    function loadMediatorData() {
        var mediatorSelect = $(".mediatorSelectpicker");
        $.ajax({
            type: "GET",
            url: prefix + "/mediatorUsers",
            dataType: "json",
            success: function(res) {
                if (res.code == 0) {
                    var data = res.data;
                    mediatorSelect.empty();
                    mediatorSelect.append("<option value=''>请选择调解员</option>");
                    $.each(data, function(index, user) {
                        mediatorSelect.append("<option value='" + user.userName + "'>" + user.userName + "</option>");
                    });
                    mediatorSelect.select2({
                        placeholder: "请选择调解员",
                        allowClear: true
                    });
                }
            }
        });
    }
    
    /* 加载渠道方数据 */
    function loadChannelData() {
        var channelSelect = $(".channelSelectpicker");
        $.ajax({
            type: "GET",
            url: prefix + "/channelList",
            dataType: "json",
            success: function(res) {
                if (res.code == 0) {
                    var data = res.data;
                    channelSelect.empty();
                    channelSelect.append("<option value=''>请选择渠道方</option>");
                    $.each(data, function(index, channel) {
                        channelSelect.append("<option value='" + channel.channelName + "'>" + channel.channelName + "</option>");
                    });
                    channelSelect.select2({
                        placeholder: "请选择渠道方",
                        allowClear: true
                    });
                }
            }
        });
    }
    
    /* 加载项目数据 */
    function loadProjectData() {
        var projectSelect = $(".projectSelectpicker");
        $.ajax({
            type: "GET",
            url: prefix + "/projectList",
            dataType: "json",
            success: function(res) {
                if (res.code == 0) {
                    var data = res.data;
                    projectSelect.empty();
                    projectSelect.append("<option value=''>请选择项目</option>");
                    $.each(data, function(index, project) {
                        projectSelect.append("<option value='" + project.projectName + "'>" + project.projectName + "</option>");
                    });
                    projectSelect.select2({
                        placeholder: "请选择项目",
                        allowClear: true
                    });
                }
            }
        });
    }
    
    // 重置表单并刷新表格
    function resetPre() {
        $("#case-form")[0].reset();
        $(".deptSelectpicker").val(null).trigger('change');
        $(".adminSelectpicker").val(null).trigger('change');
        $(".mediatorSelectpicker").val(null).trigger('change');
        $(".channelSelectpicker").val(null).trigger('change');
        $(".projectSelectpicker").val(null).trigger('change');
        $.table.search();
    }
    
    
    // 提交表单事件处理
    $("#caseModalSubmitBtn").on('click', function() {
        if (validateForm()) {
            var formData = $('#caseEditForm').serialize();
            $.ajax({
                type: 'POST',
                url: prefix + "/add",
                data: formData,
                success: function(result) {
                    if (result.code == 0) {
                        $.modal.alertSuccess(result.msg);
                        $('#caseModal').modal('hide');
                        $.table.refresh();
                    } else {
                        $.modal.alertError(result.msg);
                    }
                },
                error: function() {
                    $.modal.alertError("系统错误，请稍后重试！");
                }
            });
        }
    });
    
    // 表单验证
    function validateForm() {
        var company = $('select[name="company"]').val();
        var admin = $('select[name="admin"]').val();
        
        if (!company) {
            $.modal.alertWarning("请选择公司");
            return false;
        }
        if (!admin) {
            $.modal.alertWarning("请选择管理员");
            return false;
        }
        
        return true;
    }
      // 扩展$.table.importExcel方法，初始化导入表单的下拉菜单
    var _importExcel = $.table.importExcel;
    $.table.importExcel = function(formId, width, height) {
        // 使用与添加页面一致的尺寸
        _importExcel(formId, "800", "650");
        
        // 延迟执行，确保对话框已经打开
        setTimeout(function() {
            // 初始化导入表单中的公司下拉框
            $.ajax({
                type: "GET",
                url: prefix + "/deptTree",
                dataType: "json",
                success: function(data) {
                    var companySelect = $('select[name="company"]');
                    companySelect.empty();
                    companySelect.append("<option value=''>请选择公司</option>");
                    $.each(data, function(index, dept) {
                        companySelect.append("<option value='" + dept.name + "'>" + dept.name + "</option>");
                    });
                    companySelect.select2({
                        placeholder: "请选择公司",
                        allowClear: true
                    });
                }
            });
            
            // 初始化导入表单中的管理员下拉框
            $.ajax({
                type: "GET",
                url: prefix + "/adminUsers",
                dataType: "json",
                success: function(res) {
                    if (res.code == 0) {
                        var data = res.data;
                        var adminSelect = $('select[name="admin"]');
                        adminSelect.empty();
                        adminSelect.append("<option value=''>请选择管理员</option>");
                        $.each(data, function(index, user) {
                            adminSelect.append("<option value='" + user.userName + "'>" + user.userName + "</option>");
                        });
                        adminSelect.select2({
                            placeholder: "请选择管理员",
                            allowClear: true
                        });
                    }
                }
            });
            
            // 初始化导入表单中的调解员下拉框
            $.ajax({
                type: "GET",
                url: prefix + "/mediatorUsers",
                dataType: "json",
                success: function(res) {
                    if (res.code == 0) {
                        var data = res.data;
                        var mediatorSelect = $('select[name="mediator"]');
                        mediatorSelect.empty();
                        mediatorSelect.append("<option value=''>请选择调解员</option>");
                        $.each(data, function(index, user) {
                            mediatorSelect.append("<option value='" + user.userName + "'>" + user.userName + "</option>");
                        });
                        mediatorSelect.select2({
                            placeholder: "请选择调解员",
                            allowClear: true
                        });
                    }
                }
            });
            
            // 初始化导入表单中的渠道方下拉框
            $.ajax({
                type: "GET",
                url: prefix + "/channelList",
                dataType: "json",
                success: function(res) {
                    if (res.code == 0) {
                        var data = res.data;
                        var channelSelect = $('select[name="channel"]');
                        channelSelect.empty();
                        channelSelect.append("<option value=''>请选择渠道方</option>");
                        $.each(data, function(index, channel) {
                            channelSelect.append("<option value='" + channel.channelName + "'>" + channel.channelName + "</option>");
                        });
                        channelSelect.select2({
                            placeholder: "请选择渠道方",
                            allowClear: true
                        });
                    }
                }
            });
            
            // 初始化导入表单中的项目下拉框
            $.ajax({
                type: "GET",
                url: prefix + "/projectList",
                dataType: "json",
                success: function(res) {
                    if (res.code == 0) {
                        var data = res.data;
                        var projectSelect = $('select[name="project"]');
                        projectSelect.empty();
                        projectSelect.append("<option value=''>请选择项目</option>");
                        $.each(data, function(index, project) {
                            projectSelect.append("<option value='" + project.projectName + "'>" + project.projectName + "</option>");
                        });
                        projectSelect.select2({
                            placeholder: "请选择项目",
                            allowClear: true
                        });
                    }
                }
            });
              // 初始化日期选择器
            $("input[name='caseDate']").datepicker({
                format: "yyyy-mm-dd",
                autoclose: true,
                language: "zh-CN"
            });
            
            // 加载select2样式
            if ($.fn.select2 !== undefined) {
                $("select.form-control:not(.noselect2)").each(function () {
                    $(this).select2().on("change", function () {
                        $(this).valid();
                    })
                })
            }
        }, 200);
    };
    
    // 扩展$.table.importTemplate方法，添加附加参数
    var _importTemplate = $.table.importTemplate;
    $.table.importTemplate = function() {
        // 获取表单中选择的字段值
        var company = $('select[name="company"]').val();
        var admin = $('select[name="admin"]').val();
        var channel = $('select[name="channel"]').val();
        var project = $('select[name="project"]').val();
        var mediator = $('select[name="mediator"]').val();
        var caseDate = $('input[name="caseDate"]').val();
        
        var url = table.options.importUrl.replace('/importData', '/importTemplate');
        
        // 添加额外参数
        if (company) url += "?company=" + encodeURIComponent(company);
        if (admin) url += (url.indexOf('?') > -1 ? '&' : '?') + "admin=" + encodeURIComponent(admin);
        if (channel) url += (url.indexOf('?') > -1 ? '&' : '?') + "channel=" + encodeURIComponent(channel);
        if (project) url += (url.indexOf('?') > -1 ? '&' : '?') + "project=" + encodeURIComponent(project);
        if (mediator) url += (url.indexOf('?') > -1 ? '&' : '?') + "mediator=" + encodeURIComponent(mediator);
        if (caseDate) url += (url.indexOf('?') > -1 ? '&' : '?') + "caseDate=" + encodeURIComponent(caseDate);
        
        window.location.href = url;
    };
</script>

<!-- 导入区域 -->
<script id="importTpl" type="text/template">
<form enctype="multipart/form-data" class="form-horizontal m" id="form-case-import">
    <div class="form-group">
        <label class="col-sm-3 control-label">公司：</label>
        <div class="col-sm-8">
            <select name="company" class="form-control" data-placeholder="请选择公司">
                <option value="">请选择公司</option>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label">管理员：</label>
        <div class="col-sm-8">
            <select name="admin" class="form-control" data-placeholder="请选择管理员">
                <option value="">请选择管理员</option>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label">渠道方：</label>
        <div class="col-sm-8">
            <select name="channel" class="form-control" data-placeholder="请选择渠道方">
                <option value="">请选择渠道方</option>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label">项目：</label>
        <div class="col-sm-8">
            <select name="project" class="form-control" data-placeholder="请选择项目">
                <option value="">请选择项目</option>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label">调解员：</label>
        <div class="col-sm-8">
            <select name="mediator" class="form-control" data-placeholder="请选择调解员">
                <option value="">请选择调解员</option>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label">日期：</label>
        <div class="col-sm-8">
            <input type="text" class="form-control time-input" name="caseDate" placeholder="请选择日期">
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label">Excel文件：</label>
        <div class="col-sm-8">
            <input type="file" id="file" name="file" />
            <div class="mt10">
                <input type="checkbox" id="updateSupport" name="updateSupport" title="如果数据已经存在，更新这条数据。"> 是否更新已经存在的案件数据
                &nbsp;<a onclick="$.table.importTemplate()" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入"xls"或"xlsx"格式文件！
            </font>
        </div>
    </div>
</form>
</script>
</body>
</html>