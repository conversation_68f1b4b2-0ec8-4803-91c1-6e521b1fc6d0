package com.ruoyi.system.service;

import com.ruoyi.system.domain.MediationCase;
import java.util.List;
import java.util.Map;

public interface IMediationCaseService {
    MediationCase selectMediationCaseById(Long id);
    List<MediationCase> selectMediationCaseList(MediationCase mediationCase);
    int insertMediationCase(MediationCase mediationCase);
    int updateMediationCase(MediationCase mediationCase);
    int deleteMediationCaseById(Long id);
    int deleteMediationCaseByIds(Long[] ids);
      /**
     * 获取案件统计数据
     * @param params 查询参数
     * @return 包含总数、处理中、已确认等数据的Map
     */
    Map<String, Object> getCaseStatistics(Map<String, Object> params);
    
    /**
     * 导入案件数据
     * 
     * @param caseList 案件数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    String importMediationCase(List<MediationCase> caseList, Boolean isUpdateSupport);
}