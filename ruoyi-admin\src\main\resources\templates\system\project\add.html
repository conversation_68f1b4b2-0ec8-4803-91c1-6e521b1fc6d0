<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增项目')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-project-add">
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">项目名称：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" name="projectName" id="projectName" required>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">项目状态：</label>
                <div class="col-sm-8">
                    <div class="radio-box" th:each="dict : ${@dict.getType('sys_normal_disable')}">
                        <input type="radio" th:id="${'status_' + dict.dictCode}" name="status" th:value="${dict.dictValue}" th:checked="${dict.default}">
                        <label th:for="${'status_' + dict.dictCode}" th:text="${dict.dictLabel}"></label>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">备注：</label>
                <div class="col-sm-8">
                    <textarea id="remark" name="remark" class="form-control"></textarea>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
        var prefix = ctx + "system/project";
        
        $("#form-project-add").validate({
            onkeyup: false,
            rules:{
                projectName:{
                    remote: {
                        url: prefix + "/checkProjectNameUnique",
                        type: "post",
                        dataType: "json",
                        data: {
                            "projectName": function() {
                                return $.common.trim($("#projectName").val());
                            }
                        },
                        dataFilter: function(data, type) {
                            return $.validate.unique(data);
                        }
                    }
                },
            },
            messages: {
                "projectName": {
                    remote: "项目名称已经存在"
                }
            },
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-project-add').serialize());
            }
        }
    </script>
</body>
</html>
