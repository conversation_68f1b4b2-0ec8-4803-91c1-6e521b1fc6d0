<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <title>登录系统</title>
    <meta name="description" content="管理系统">
    <link href="../static/css/bootstrap.min.css" th:href="@{/css/bootstrap.min.css}" rel="stylesheet"/>
    <link href="../static/css/font-awesome.min.css" th:href="@{/css/font-awesome.min.css}" rel="stylesheet"/>
    <link href="../static/ruoyi/css/ry-ui.css" th:href="@{/ruoyi/css/ry-ui.css?v=4.8.0}" rel="stylesheet"/>
    <!-- 360浏览器急速模式 -->
    <meta name="renderer" content="webkit">
    <!-- 避免IE使用兼容模式 -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link rel="shortcut icon" href="../static/favicon.ico" th:href="@{favicon.ico}"/>
    <style type="text/css">
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
            font-family: 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif;
            overflow: hidden;
        }
        
        body {
            background-image: url(../static/img/login-background.jpg);
            background-image: url([[@{/img/login-background.jpg}]]);
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .login-container {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }
        
        .login-box {
            width: 380px;
            background: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 30px;
            z-index: 10;
        }
        
        .login-logo {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-logo h3 {
            color: #666;
            font-size: 22px;
            font-weight: normal;
            margin: 0;
        }
        
        .input-group {
            position: relative;
            margin-bottom: 15px;
        }
        
        .input-group input {
            width: 152%;
            height: 45px;
            border: 1px solid #eee;
            border-radius: 4px;
            padding: 10px 10px 10px 40px;
            color: #333;
            font-size: 14px;
            transition: all 0.3s;
            /* background-color: #F5F5F5; */
        }
        
        .input-group input:focus {
            border-color: #ddd;
            outline: none;
            box-shadow: none;
        }
        
        .input-group i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #aaa;
            font-size: 16px;
        }
        
        .captcha-group {
            display: flex;
            margin-bottom: 15px;
        }
        
        .captcha-group .input-group {
            flex: 1;
            margin-bottom: 0;
            margin-right: 10px;
        }
        
        .captcha-img {
            height: 45px;
            border-radius: 4px;
            overflow: hidden;
            background-color: #f7f9fa;
        }
        
        .captcha-img img {
            height: 100%;
            cursor: pointer;
        }
        
        .remember-box {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
        }
        
        .remember-box input[type="checkbox"] {
            width: 16px;
            height: 16px;
            margin-right: 5px;
            border: 1px solid #ddd;
            position: relative;
        }
        
        .remember-box label {
            font-size: 14px;
            color: #999;
            cursor: pointer;
            user-select: none;
        }
        
        .login-btn {
            width: 100%;
            height: 45px;
            background: #2e93ff;
            border: none;
            border-radius: 4px;
            color: white;
            font-size: 16px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .login-btn:hover {
            background: #1a88ff;
        }
        
        .footer {
            position: absolute;
            bottom: 20px;
            width: 100%;
            text-align: center;
            color: #fff;
            font-size: 12px;
            text-shadow: 0 1px 2px rgba(0,0,0,0.5);
        }
        
        label.error {
            color: #f56c6c;
            font-size: 12px;
            margin-top: 5px;
            margin-bottom: 5px;
            display: block;
        }
    </style>
    <script>
        if(window.top!==window.self){alert('未登录或登录超时。请重新登录');window.top.location=window.location};
    </script>
</head>
<body>
    <div class="login-container">
        <div class="login-box">
            <div class="login-logo">
                <h3>调解管理系统</h3>
            </div>
            <form id="signupForm" autocomplete="off">
                <div class="input-group">
                    <i class="fa fa-user"></i>
                    <input type="text" name="username" placeholder="用户名" value="admin" />
                </div>
                <div class="input-group">
                    <i class="fa fa-lock"></i>
                    <input type="password" name="password" placeholder="密码" value="admin123" />
                </div>
                <div class="captcha-group" th:if="${captchaEnabled==true}">
                    <div class="input-group">
                        <i class="fa fa-shield"></i>
                        <input type="text" style="width: 100%;" name="validateCode" placeholder="验证码" maxlength="5" />
                    </div>
                    <a href="javascript:void(0);" title="点击更换验证码" class="captcha-img">
                        <img th:src="@{/captcha/captchaImage(type=${captchaType})}" class="imgcode" />
                    </a>
                </div>
                <div class="remember-box" th:if="${isRemembered}">
                    <input type="checkbox" id="rememberme" name="rememberme"> 
                    <label for="rememberme">记住密码</label>
                </div>
                <button class="login-btn" id="btnSubmit" data-loading="正在验证登录，请稍候...">登 录</button>
            </form>
        </div>
        <div class="footer">
            Copyright © 2018-2025 ruoyi.vip All Rights Reserved.
        </div>
    </div>

<script th:inline="javascript"> var ctx = [[@{/}]]; var captchaType = [[${captchaType}]]; var captchaEnabled = [[${captchaEnabled}]];</script>
<!--[if lte IE 8]><script>window.location.href=ctx+'html/ie.html';</script><![endif]-->
<!-- 全局js -->
<script src="../static/js/jquery.min.js" th:src="@{/js/jquery.min.js}"></script>
<script src="../static/ajax/libs/validate/jquery.validate.min.js" th:src="@{/ajax/libs/validate/jquery.validate.min.js}"></script>
<script src="../static/ajax/libs/layer/layer.min.js" th:src="@{/ajax/libs/layer/layer.min.js}"></script>
<script src="../static/ajax/libs/blockUI/jquery.blockUI.js" th:src="@{/ajax/libs/blockUI/jquery.blockUI.js}"></script>
<script src="../static/ruoyi/js/ry-ui.js" th:src="@{/ruoyi/js/ry-ui.js?v=4.8.0}"></script>
<script src="../static/ruoyi/login.js" th:src="@{/ruoyi/login.js}"></script>
</body>
</html>
