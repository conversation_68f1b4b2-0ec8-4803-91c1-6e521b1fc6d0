package com.ruoyi.system.service.impl;

import com.ruoyi.system.domain.MediationCase;
import com.ruoyi.system.mapper.MediationCaseMapper;
import com.ruoyi.system.service.IMediationCaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class MediationCaseServiceImpl implements IMediationCaseService {
    @Autowired
    private MediationCaseMapper mediationCaseMapper;

    @Override
    public MediationCase selectMediationCaseById(Long id) {
        return mediationCaseMapper.selectMediationCaseById(id);
    }

    @Override
    public List<MediationCase> selectMediationCaseList(MediationCase mediationCase) {
        return mediationCaseMapper.selectMediationCaseList(mediationCase);
    }

    @Override
    public int insertMediationCase(MediationCase mediationCase) {
        return mediationCaseMapper.insertMediationCase(mediationCase);
    }

    @Override
    public int updateMediationCase(MediationCase mediationCase) {
        return mediationCaseMapper.updateMediationCase(mediationCase);
    }

    @Override
    public int deleteMediationCaseById(Long id) {
        return mediationCaseMapper.deleteMediationCaseById(id);
    }

    @Override
    public int deleteMediationCaseByIds(Long[] ids) {
        return mediationCaseMapper.deleteMediationCaseByIds(ids);
    }
    
    @Override
    public Map<String, Object> getCaseStatistics(Map<String, Object> params) {
        // 查询条件
        MediationCase query = new MediationCase();
        
        // 如果有公司部门ID
        if (params.containsKey("deptId")) {
            query.setDeptId(Long.valueOf(params.get("deptId").toString()));
        }
        
        // 如果有管理员ID
        if (params.containsKey("adminId")) {
            query.setAdminId(Long.valueOf(params.get("adminId").toString()));
        }
        
        // 如果有渠道ID
        if (params.containsKey("channelId")) {
            query.setChannelId(Long.valueOf(params.get("channelId").toString()));
        }
        
        // 如果有调解员ID
        if (params.containsKey("mediatorId")) {
            query.setMediatorId(Long.valueOf(params.get("mediatorId").toString()));
        }
        
        // 获取所有符合条件的案件
        List<MediationCase> allCases = mediationCaseMapper.selectMediationCaseList(query);
        
        // 统计结果
        Map<String, Object> result = new HashMap<>();
        int totalCount = allCases.size();
        int processingCount = 0;  // 处理中 - status=1
        int confirmedCount = 0;   // 已确认 - status=4
        
        // 遍历统计不同状态的案件数量
        for (MediationCase mediationCase : allCases) {
            if ("1".equals(mediationCase.getStatus())) {
                processingCount++;
            } else if ("4".equals(mediationCase.getStatus())) {
                confirmedCount++;
            }
        }
        
        // 封装结果        result.put("totalCount", totalCount);
        result.put("processingCount", processingCount);
        result.put("confirmedCount", confirmedCount);
        
        return result;
    }
    
    /**
     * 导入案件数据
     * 
     * @param caseList 案件数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importMediationCase(List<MediationCase> caseList, Boolean isUpdateSupport) {
        if (caseList == null || caseList.isEmpty()) {
            return "导入案件数据不能为空！";
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        
        for (MediationCase mediationCase : caseList) {
            try {
                // 验证是否存在这个案件
                MediationCase existCase = new MediationCase();
                // 可以根据实际情况设置唯一标识，这里假设根据原告、被告和案件日期来判断
                if (mediationCase.getPlaintiff() != null) {
                    existCase.setPlaintiff(mediationCase.getPlaintiff());
                }
                if (mediationCase.getDefendant() != null) {
                    existCase.setDefendant(mediationCase.getDefendant());
                }
                if (mediationCase.getCaseDate() != null) {
                    existCase.setCaseDate(mediationCase.getCaseDate());
                }
                
                List<MediationCase> existCaseList = mediationCaseMapper.selectMediationCaseList(existCase);
                
                if (!existCaseList.isEmpty() && isUpdateSupport) {
                    // 更新已有数据
                    MediationCase dbCase = existCaseList.get(0);
                    mediationCase.setId(dbCase.getId());
                    mediationCaseMapper.updateMediationCase(mediationCase);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、案件 ").append(mediationCase.getPlaintiff()).append(" 更新成功");
                } else if (existCaseList.isEmpty()) {
                    // 新增数据
                    mediationCaseMapper.insertMediationCase(mediationCase);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、案件 ").append(mediationCase.getPlaintiff()).append(" 导入成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、案件 ").append(mediationCase.getPlaintiff()).append(" 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、案件 " + mediationCase.getPlaintiff() + " 导入失败：";
                failureMsg.append(msg).append(e.getMessage());
            }
        }
        
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            return failureMsg.toString();
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
            return successMsg.toString();
        }
    }
}