package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.SysChannel;
import com.ruoyi.system.mapper.SysChannelMapper;
import com.ruoyi.system.service.ISysChannelService;

/**
 * 渠道方管理 服务层实现
 * 
 */
@Service
public class SysChannelServiceImpl implements ISysChannelService 
{
    @Autowired
    private SysChannelMapper channelMapper;

    /**
     * 查询渠道方数据集合
     * 
     * @param channel 渠道方信息
     * @return 渠道方数据集合
     */
    @Override
    public List<SysChannel> selectChannelList(SysChannel channel)
    {
        return channelMapper.selectChannelList(channel);
    }

    /**
     * 查询所有渠道方
     * 
     * @return 渠道方列表
     */
    @Override
    public List<SysChannel> selectChannelAll()
    {
        return channelMapper.selectChannelAll();
    }

    /**
     * 通过渠道ID查询渠道方信息
     * 
     * @param channelId 渠道ID
     * @return 渠道方信息
     */
    @Override
    public SysChannel selectChannelById(Long channelId)
    {
        return channelMapper.selectChannelById(channelId);
    }

    /**
     * 校验渠道名称是否唯一
     * 
     * @param channel 渠道方信息
     * @return 结果
     */
    @Override
    public String checkChannelNameUnique(SysChannel channel)
    {
        Long channelId = StringUtils.isNull(channel.getChannelId()) ? -1L : channel.getChannelId();
        SysChannel info = channelMapper.selectChannelByName(channel.getChannelName());
        if (StringUtils.isNotNull(info) && info.getChannelId().longValue() != channelId.longValue())
        {
            return UserConstants.CHANNEL_NAME_NOT_UNIQUE;
        }
        return UserConstants.CHANNEL_NAME_UNIQUE;
    }

    /**
     * 新增渠道方信息
     * 
     * @param channel 渠道方信息
     * @return 结果
     */
    @Override
    public int insertChannel(SysChannel channel)
    {
        return channelMapper.insertChannel(channel);
    }

    /**
     * 修改渠道方信息
     * 
     * @param channel 渠道方信息
     * @return 结果
     */
    @Override
    public int updateChannel(SysChannel channel)
    {
        return channelMapper.updateChannel(channel);
    }

    /**
     * 删除渠道方信息
     * 
     * @param channelId 渠道ID
     * @return 结果
     */
    @Override
    public int deleteChannelById(Long channelId)
    {
        return channelMapper.deleteChannelById(channelId);
    }

    /**
     * 批量删除渠道方信息
     * 
     * @param channelIds 需要删除的渠道ID
     * @return 结果
     */
    @Override
    public int deleteChannelByIds(Long[] channelIds)
    {
        return channelMapper.deleteChannelByIds(channelIds);
    }
}
