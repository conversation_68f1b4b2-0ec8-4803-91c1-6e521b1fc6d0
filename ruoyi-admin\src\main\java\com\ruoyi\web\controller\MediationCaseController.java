package com.ruoyi.web.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.Ztree;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.domain.MediationCase;
import com.ruoyi.system.domain.SysChannel;
import com.ruoyi.system.domain.SysProject;
import com.ruoyi.system.service.IMediationCaseService;
import com.ruoyi.system.service.ISysChannelService;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.ISysProjectService;
import com.ruoyi.system.service.ISysRoleService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

@Controller
@RequestMapping("/mediation/case")
public class MediationCaseController extends BaseController {
    private String prefix = "mediation/case";

    @Autowired
    private IMediationCaseService mediationCaseService;

    @Autowired
    private ISysDeptService deptService;
    
    @Autowired
    private ISysUserService userService;
    
    @Autowired
    private ISysRoleService roleService;
    
    @Autowired
    private ISysChannelService channelService;
    
    @Autowired
    private ISysProjectService projectService;    /**
     * 打开案件列表页面
     */
    @GetMapping()
    public String casePage() {
        return prefix + "/case";
    }
    
    /**
     * GET请求获取案件列表数据
     */
    @GetMapping("/list")
    @ResponseBody
    public TableDataInfo list(MediationCase mediationCase) {
        startPage();
        List<MediationCase> list = mediationCaseService.selectMediationCaseList(mediationCase);
        return getDataTable(list);
    }
    
    /**
     * POST请求获取案件列表数据
     */
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo postList(MediationCase mediationCase) {
        startPage();
        List<MediationCase> list = mediationCaseService.selectMediationCaseList(mediationCase);
        return getDataTable(list);
    }
    // 注意：明确的路径映射应该放在通配符路径映射之前
    /**
     * 新增案件
     */
    @GetMapping("/add")
    public String addPage() {
        return prefix + "/add";
    }
      /**
     * 新增保存案件
     */
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult add(MediationCase mediationCase) {
        return toAjax(mediationCaseService.insertMediationCase(mediationCase));
    }
      /**
     * 获取部门树列表数据
     */
    @GetMapping("/deptTree")
    @ResponseBody
    public List<Ztree> deptTreeData() {
        List<Ztree> ztrees = deptService.selectDeptTree(new SysDept());
        return ztrees;
    }
    
    /**
     * 获取部门列表数据
     */
    @GetMapping("/deptList")
    @ResponseBody
    public AjaxResult getDeptList() {
        List<SysDept> depts = deptService.selectDeptList(new SysDept());
        return AjaxResult.success(depts);
    }
      /**
     * 获取调解员用户列表数据
     */    @GetMapping("/mediatorUsers")
    @ResponseBody
    public AjaxResult getMediatorUsers() {
        SysRole role = new SysRole();
        role.setRoleKey("mediator");
        // 使用替代方法获取用户列表
        List<SysUser> users = getUsersByRole(role.getRoleKey());
        return AjaxResult.success(users);
    }
    
    /**
     * 根据角色标识获取用户列表
     */
    private List<SysUser> getUsersByRole(String roleKey) {
        SysRole roleParam = new SysRole();
        roleParam.setRoleKey(roleKey);
        List<SysRole> roles = roleService.selectRoleList(roleParam);
        if (roles != null && !roles.isEmpty()) {
            SysRole targetRole = roles.get(0);
            SysUser userParam = new SysUser();
            userParam.setRoleId(targetRole.getRoleId());
            return userService.selectAllocatedList(userParam);
        }
        return new ArrayList<>();
    }
      /**
     * 获取管理员用户列表数据
     */
    @GetMapping("/adminUsers")
    @ResponseBody
    public AjaxResult getAdminUsers() {
        SysRole role = new SysRole();
        role.setRoleKey("admin");
        // 使用替代方法获取用户列表
        List<SysUser> users = getUsersByRole(role.getRoleKey());
        return AjaxResult.success(users);
    }    /**
     * 获取渠道方列表数据
     */
    @GetMapping("/channelList")
    @ResponseBody
    public AjaxResult getChannelList() {
        List<SysChannel> channels = channelService.selectChannelAll();
        return AjaxResult.success(channels);
    }
    
    /**
     * 获取项目列表数据
     */
    @GetMapping("/projectList")
    @ResponseBody
    public AjaxResult getProjectList() {
        List<SysProject> projects = projectService.selectProjectAll();
        return AjaxResult.success(projects);
    }
      /**
     * 获取案件详情
     */
    @GetMapping("/{id}")
    @ResponseBody
    public AjaxResult getInfo(@PathVariable Long id) {
        return AjaxResult.success(mediationCaseService.selectMediationCaseById(id));
    }    /**
     * 修改案件
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        MediationCase mediationCase = mediationCaseService.selectMediationCaseById(id);
        mmap.put("mediationCase", mediationCase);
        return prefix + "/edit";
    }
    
    /**
     * 修改保存案件
     */
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult edit(MediationCase mediationCase) {
        return toAjax(mediationCaseService.updateMediationCase(mediationCase));
    }    /**
     * 删除案件
     * 支持单个ID和多个ID的删除
     */
    @RequestMapping(value = "/remove/{ids}", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(mediationCaseService.deleteMediationCaseByIds(ids));
    }    /**
     * 导出案件
     */
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(MediationCase mediationCase) {
        List<MediationCase> list = mediationCaseService.selectMediationCaseList(mediationCase);
        com.ruoyi.common.utils.poi.ExcelUtil<MediationCase> util = new com.ruoyi.common.utils.poi.ExcelUtil<MediationCase>(MediationCase.class);
        return util.exportExcel(list, "案件数据");
    }    /**
     * 下载导入模板
     */
    @GetMapping("/importTemplate")
    @ResponseBody
    public AjaxResult importTemplate(
            @RequestParam(required = false) String company,
            @RequestParam(required = false) String admin,
            @RequestParam(required = false) String channel,
            @RequestParam(required = false) String project,
            @RequestParam(required = false) String mediator,
            @RequestParam(required = false) String caseDate) {
        
        MediationCase template = new MediationCase();
        if (company != null) {
            template.setCompanyName(company);
        }
        if (admin != null) {
            template.setAdminName(admin);
        }
        if (channel != null) {
            template.setChannelName(channel);
        }
        if (project != null) {
            template.setProjectName(project);
        }
        if (mediator != null) {
            template.setMediatorName(mediator);
        }
        if (caseDate != null) {
            try {
                template.setCaseDate(new java.text.SimpleDateFormat("yyyy-MM-dd").parse(caseDate));
            } catch (Exception e) {
                // 日期格式错误时不设置
            }
        }
        
        List<MediationCase> templateList = new ArrayList<>();
        templateList.add(template);
        
        com.ruoyi.common.utils.poi.ExcelUtil<MediationCase> util = new com.ruoyi.common.utils.poi.ExcelUtil<MediationCase>(MediationCase.class);
        return util.exportExcel(templateList, "案件导入模板");
    }
      /**
     * 批量导入案件数据
     */
    @PostMapping("/importData")
    @ResponseBody
    public AjaxResult importData(
            MultipartFile file, 
            boolean updateSupport,
            @RequestParam(required = false) String company,
            @RequestParam(required = false) String admin,
            @RequestParam(required = false) String channel,
            @RequestParam(required = false) String project,
            @RequestParam(required = false) String mediator,
            @RequestParam(required = false) String caseDate) throws Exception {
        
        com.ruoyi.common.utils.poi.ExcelUtil<MediationCase> util = new com.ruoyi.common.utils.poi.ExcelUtil<MediationCase>(MediationCase.class);
        List<MediationCase> caseList = util.importExcel(file.getInputStream());
        
        // 为所有导入的案件设置共同的表单字段
        if (caseList != null && !caseList.isEmpty()) {
            for (MediationCase mediationCase : caseList) {
                // 如果Excel中没有设置这些字段，则使用表单中的值
                if (company != null && (mediationCase.getCompanyName() == null || mediationCase.getCompanyName().trim().isEmpty())) {
                    mediationCase.setCompanyName(company);
                }
                if (admin != null && (mediationCase.getAdminName() == null || mediationCase.getAdminName().trim().isEmpty())) {
                    mediationCase.setAdminName(admin);
                }
                if (channel != null && (mediationCase.getChannelName() == null || mediationCase.getChannelName().trim().isEmpty())) {
                    mediationCase.setChannelName(channel);
                }
                if (project != null && (mediationCase.getProjectName() == null || mediationCase.getProjectName().trim().isEmpty())) {
                    mediationCase.setProjectName(project);
                }
                if (mediator != null && (mediationCase.getMediatorName() == null || mediationCase.getMediatorName().trim().isEmpty())) {
                    mediationCase.setMediatorName(mediator);
                }
                if (caseDate != null && mediationCase.getCaseDate() == null) {
                    try {
                        mediationCase.setCaseDate(new java.text.SimpleDateFormat("yyyy-MM-dd").parse(caseDate));
                    } catch (Exception e) {
                        // 日期格式错误时不设置
                    }
                }
            }
        }
        
        String message = mediationCaseService.importMediationCase(caseList, updateSupport);
        return AjaxResult.success(message);
    }
}